#include "common.cuh"
#include "curand_kernel.h"
#include "kernel.h"
#include "cuda_memory.hpp"
#include "types.cuh"
#include "math.h"
#include "warp_ops.cuh"
#include <cmath>
#include <vector>
/* Original Include files */
#include "cache.h"
#include "coords.h"
#include "model.h"
#include "monte_carlo.h"
#include "mutate.h"
#include "precalculate.h"
#include "quasi_newton.h"
#include <cooperative_groups.h>
#include <cooperative_groups/reduce.h>

namespace cg = cooperative_groups;

template <typename Config>
std::vector<output_type> cuda_to_vina(output_type_cuda_t_<Config> results_ptr[],
                                      int thread);

/* Below is monte-carlo kernel, based on kernel.cl*/
template<typename Config>
__device__ __forceinline__ void get_heavy_atom_movable_coords(output_type_cuda_t_<Config> *tmp,
                                                              const m_cuda_t_<Config> *m_cuda_gpu) {
    int counter = 0;
    for (int i = 0; i < m_cuda_gpu->m_num_movable_atoms; i++) {
        if (m_cuda_gpu->atoms[i].types[0] != EL_TYPE_H) {
            for (int j = 0; j < 3; j++) tmp->coords[counter][j] = m_cuda_gpu->m_coords.coords[i][j];
            counter++;
        } else {
            // DEBUG_PRINTF("\n P2: removed H atom coords in
            // get_heavy_atom_movable_coords()!");
        }
    }
    /* assign 0 for others */
    for (int i = counter; i < Config::MAX_NUM_OF_ATOMS_; i++) {
        for (int j = 0; j < 3; j++) tmp->coords[i][j] = 0;
    }
}

__device__ __forceinline__ float generate_n(const float *pi_map, const int step) {
    return fabs(pi_map[step]) / M_PI_F;
}

__device__ __forceinline__ bool metropolis_accept(float old_f, float new_f, float temperature,
                                                  float n) {
    if (new_f < old_f) return true;
    const float acceptance_probability = exp((old_f - new_f) / temperature);
    return n < acceptance_probability;
}

__device__ __forceinline__ void write_back(output_type_cuda_t *results,
                                           const output_type_cuda_t *best_out) {
    memcpy(results->position, best_out->position, 3 * sizeof(float));
    memcpy(results->orientation, best_out->orientation, 4 * sizeof(float));
    memcpy(results->lig_torsion, best_out->lig_torsion,
           DefaultConfig::MAX_NUM_OF_LIG_TORSION * sizeof(float));
    memcpy(results->flex_torsion, best_out->flex_torsion,
           DefaultConfig::MAX_NUM_OF_FLEX_TORSION * sizeof(float));
    results->lig_torsion_size = best_out->lig_torsion_size;
    results->e = best_out->e;
    memcpy(results->coords, best_out->coords,
           DefaultConfig::MAX_NUM_OF_ATOMS * 3 * sizeof(float));
}
// MAX_THREADS_PER_BLOCK and MIN_BLOCKS_PER_MP should be adjusted according to
// the profiling results
#define MAX_THREADS_PER_BLOCK 128
#define MIN_BLOCKS_PER_MP 16
template <typename Config = DefaultConfig, unsigned TileSize = 32>
__global__ __launch_bounds__(MAX_THREADS_PER_BLOCK, MIN_BLOCKS_PER_MP) void kernel(
    m_cuda_t_<Config> *m_cuda_global, ig_cuda_t_<Config> *ig_cuda_gpu, p_cuda_t_<Config> *p_cuda_gpu,
    float *rand_molec_struc_gpu, int bfgs_max_steps, float mutation_amplitude,
    curandStatePhilox4_32_10_t *states, unsigned long long seed, float epsilon_fl,
    float *hunt_cap_gpu, float *authentic_v_gpu, output_type_cuda_t_<Config> *results,
    output_type_cuda_t_<Config> *output_aux, change_cuda_t_<Config> *change_aux, pot_cuda_t_<Config> *pot_aux,
    matrix_d_<Config> *h_cuda_gpu, m_cuda_t_<Config> *m_cuda_gpu, int search_depth, int num_of_ligands,
    int threads_per_ligand) {
    int bid = blockIdx.x, tid = threadIdx.x;
    int pose_id = (bid * blockDim.x + tid) / TileSize;
    if (m_cuda_global[pose_id / threads_per_ligand].m_num_movable_atoms == -1) {
        return;
    }

    auto tb = cg::this_thread_block();
    cg::thread_block_tile<TileSize> tile = cg::tiled_partition<TileSize>(tb);

    float best_e = INFINITY;
    output_type_cuda_t_<Config> &tmp = output_aux[pose_id * 5];
    output_type_cuda_t_<Config> &best_out = output_aux[pose_id * 5 + 1];
    output_type_cuda_t_<Config> &candidate = output_aux[pose_id * 5 + 2];
    output_type_cuda_t_<Config> &x_new = output_aux[pose_id * 5 + 3];
    output_type_cuda_t_<Config> &x_orig = output_aux[pose_id * 5 + 4];

    change_cuda_t_<Config> &g = change_aux[pose_id * 6];
    change_cuda_t_<Config> &tmp1 = change_aux[pose_id * 6 + 1];
    change_cuda_t_<Config> &tmp2 = change_aux[pose_id * 6 + 2];
    change_cuda_t_<Config> &tmp3 = change_aux[pose_id * 6 + 3];
    change_cuda_t_<Config> &tmp4 = change_aux[pose_id * 6 + 4];
    change_cuda_t_<Config> &tmp5 = change_aux[pose_id * 6 + 5];

    if (pose_id < num_of_ligands * threads_per_ligand) {
        output_type_cuda_init_warp<TileSize, Config>(
            tile, &tmp, rand_molec_struc_gpu + pose_id * (Config::SIZE_OF_MOLEC_STRUC_ / sizeof(float)));

        m_cuda_init_with_m_cuda_warp<TileSize, Config>(tile, &m_cuda_global[pose_id / threads_per_ligand],
                                     &m_cuda_gpu[pose_id]);

        if (tile.thread_rank() == 0) {
            curand_init(seed, pose_id, 0, &states[pose_id]);
            g.lig_torsion_size = tmp.lig_torsion_size;
        }
        tile.sync();

        if (false) {
            ig_cuda_gpu += pose_id / threads_per_ligand;
        }

        pot_aux += pose_id;
        p_cuda_gpu += pose_id / threads_per_ligand;

        // BFGS
        for (int step = 0; step < search_depth; step++) {
            output_type_cuda_init_with_output_warp<TileSize,Config>(tile, &candidate, &tmp);

            if (tile.thread_rank() == 0)
                mutate_conf_cuda<Config>(bfgs_max_steps, &candidate, &states[pose_id],
                                 m_cuda_gpu[pose_id].ligand.begin, m_cuda_gpu[pose_id].ligand.end,
                                 m_cuda_gpu[pose_id].atoms, &m_cuda_gpu[pose_id].m_coords,
                                 m_cuda_gpu[pose_id].ligand.rigid.origin[0], epsilon_fl,
                                 mutation_amplitude);
            tile.sync();

            bfgs_warp<TileSize,Config>(tile, &candidate, &x_new, &x_orig, &g, &tmp1, &tmp2, &tmp3, &tmp4, &tmp5,
                      &h_cuda_gpu[pose_id], &m_cuda_gpu[pose_id], p_cuda_gpu, ig_cuda_gpu, pot_aux,
                      hunt_cap_gpu, epsilon_fl, bfgs_max_steps);

            bool accepted;
            if (tile.thread_rank() == 0) {
                // n ~ U[0,1]
                float n = curand_uniform(&states[pose_id]);
                accepted = metropolis_accept(tmp.e, candidate.e, 1.2, n);
            }
            accepted = tile.shfl(accepted, 0);

            if (step == 0 || accepted) {
                output_type_cuda_init_with_output_warp<TileSize,Config>(tile, &tmp, &candidate);

                if (tile.thread_rank() == 0) {
                    set<Config>(&tmp, &m_cuda_gpu[pose_id].ligand.rigid, &m_cuda_gpu[pose_id].m_coords,
                        m_cuda_gpu[pose_id].atoms, m_cuda_gpu[pose_id].m_num_movable_atoms,
                        epsilon_fl);
                }
                tile.sync();

                if (tmp.e < best_e) {
                    bfgs_warp<TileSize,Config>(tile, &tmp, &x_new, &x_orig, &g, &tmp1, &tmp2, &tmp3, &tmp4, &tmp5,
                              &h_cuda_gpu[pose_id], &m_cuda_gpu[pose_id], p_cuda_gpu, ig_cuda_gpu,
                              pot_aux, authentic_v_gpu, epsilon_fl, bfgs_max_steps);

                    // set
                    if (tmp.e < best_e) {
                        if (tile.thread_rank() == 0)
                            set<Config>(&tmp, &m_cuda_gpu[pose_id].ligand.rigid,
                                &m_cuda_gpu[pose_id].m_coords, m_cuda_gpu[pose_id].atoms,
                                m_cuda_gpu[pose_id].m_num_movable_atoms, epsilon_fl);
                        tile.sync();

                        output_type_cuda_init_with_output_warp<TileSize,Config>(tile, &best_out, &tmp);

                        if (tile.thread_rank() == 0) {
                            get_heavy_atom_movable_coords<Config>(&best_out,
                                                          &m_cuda_gpu[pose_id]);  // get coords
                        }
                        tile.sync();

                        best_e = tmp.e;
                    }
                }
            }
        }
        // write the best conformation back to CPU // FIX?? should add more
        write_back_warp<TileSize,Config>(tile, results + pose_id, &best_out);
    }
}
/* Above based on kernel.cl */

__host__ void monte_carlo::mc_stream(
    std::vector<model> &m_gpu, std::vector<output_container> &out_gpu,
    std::vector<precalculate_byatom> &p_gpu, triangular_matrix_cuda_t *m_data_list_gpu,
    const igrid &ig, const vec &corner1, const vec &corner2, rng &generator, int verbosity,
    unsigned long long seed) const {
    monte_carlo_template helper;
    helper.max_evals = max_evals;
    helper.global_steps = global_steps;
    helper.temperature = temperature;
    helper.hunt_cap = hunt_cap;
    helper.min_rmsd = min_rmsd;
    helper.num_saved_mins = num_saved_mins;
    helper.mutation_amplitude = mutation_amplitude;
    helper.local_steps = local_steps;
    helper.threads_per_ligand = threads_per_ligand;
    helper.num_of_ligands = num_of_ligands;
    helper.local_only = local_only;
    helper.thread = thread;
    helper.run_search<DefaultConfig>(m_gpu, out_gpu, p_gpu, m_data_list_gpu,
                                     ig, corner1, corner2, generator, verbosity, seed);
}

/* Below based on monte-carlo.cpp */

// #ifdef ENABLE_CUDA

template <typename Config>
std::vector<output_type> cuda_to_vina(output_type_cuda_t_<Config> results_ptr[],
                                      int thread) {
    std::vector<output_type> results_vina;
    for (int i = 0; i < thread; ++i) {
        output_type_cuda_t_<Config> results = results_ptr[i];
        conf tmp_c;
        tmp_c.ligands.resize(1);
        for (int j = 0; j < 3; j++) tmp_c.ligands[0].rigid.position[j] = results.position[j];
        qt q(results.orientation[0], results.orientation[1], results.orientation[2],
             results.orientation[3]);
        tmp_c.ligands[0].rigid.orientation = q;
        output_type tmp_vina(tmp_c, results.e);
        for (int j = 0; j < results.lig_torsion_size; j++)
            tmp_vina.c.ligands[0].torsions.push_back(results.lig_torsion[j]);
        for (int j = 0; j < Config::MAX_NUM_OF_ATOMS_; j++) {
            vec v_tmp(results.coords[j][0], results.coords[j][1], results.coords[j][2]);
            if (v_tmp[0] * v_tmp[1] * v_tmp[2] != 0) tmp_vina.coords.push_back(v_tmp);
        }
        results_vina.push_back(tmp_vina);
    }
    return results_vina;
}
__host__ void monte_carlo::operator()(
    std::vector<model> &m_gpu, std::vector<output_container> &out_gpu,
    std::vector<precalculate_byatom> &p_gpu, triangular_matrix_cuda_t *m_data_list_gpu,
    const igrid &ig, const vec &corner1, const vec &corner2, rng &generator, int verbosity,
    unsigned long long seed) const {
    monte_carlo_template helper;
    helper.max_evals = max_evals;
    helper.global_steps = global_steps;
    helper.temperature = temperature;
    helper.hunt_cap = hunt_cap;
    helper.min_rmsd = min_rmsd;
    helper.num_saved_mins = num_saved_mins;
    helper.mutation_amplitude = mutation_amplitude;
    helper.local_steps = local_steps;
    helper.threads_per_ligand = threads_per_ligand;
    helper.num_of_ligands = num_of_ligands;
    helper.local_only = local_only;
    helper.thread = thread;
    helper.run_search<DefaultConfig>(m_gpu, out_gpu, p_gpu, m_data_list_gpu,
                                     ig, corner1, corner2, generator, verbosity, seed);
}

bool metropolis_accept(fl old_f, fl new_f, fl temperature, rng &generator) {
    if (new_f < old_f) return true;
    const fl acceptance_probability = std::exp((old_f - new_f) / temperature);
    return random_fl(0, 1, generator) < acceptance_probability;
}

__host__ void monte_carlo::operator()(model &m, output_container &out, const precalculate_byatom &p,
                                      const igrid &ig, const vec &corner1, const vec &corner2,
                                      rng &generator) const {
    int evalcount = 0;
    vec authentic_v(1000, 1000,
                    1000);  // FIXME? this is here to avoid max_fl/max_fl
    conf_size s = m.get_size();
    change g(s);
    output_type tmp(s, 0);
    tmp.c.randomize(corner1, corner2, generator);
    fl best_e = max_fl;
    quasi_newton quasi_newton_par;
    quasi_newton_par.max_steps = local_steps;
    VINA_U_FOR(step, global_steps) {
        // if(increment_me)
        // 	++(*increment_me);
        if ((max_evals > 0) & (evalcount > max_evals)) break;
        output_type candidate = tmp;
        mutate_conf(candidate.c, m, mutation_amplitude, generator);
        quasi_newton_par(m, p, ig, candidate, g, hunt_cap, evalcount);
        if (step == 0 || metropolis_accept(tmp.e, candidate.e, temperature, generator)) {
            tmp = candidate;

            m.set(tmp.c);  // FIXME? useless?

            // FIXME only for very promising ones
            if (tmp.e < best_e || out.size() < num_saved_mins) {
                quasi_newton_par(m, p, ig, tmp, g, authentic_v, evalcount);
                m.set(tmp.c);  // FIXME? useless?
                tmp.coords = m.get_heavy_atom_movable_coords();
                add_to_output_container(out, tmp, min_rmsd,
                                        num_saved_mins);  // 20 - max size
                if (tmp.e < best_e) best_e = tmp.e;
            }
        }
    }
    VINA_CHECK(!out.empty());
    VINA_CHECK(out.front().e <= out.back().e);  // make sure the sorting worked in the correct order
                                      }
__host__ void monte_carlo_template::operator()(model &m, output_container &out, const precalculate_byatom &p,
                                      const igrid &ig, const vec &corner1, const vec &corner2,
                                      rng &generator) const {
    int evalcount = 0;
    vec authentic_v(1000, 1000,
                    1000);  // FIXME? this is here to avoid max_fl/max_fl
    conf_size s = m.get_size();
    change g(s);
    output_type tmp(s, 0);
    tmp.c.randomize(corner1, corner2, generator);
    fl best_e = max_fl;
    quasi_newton quasi_newton_par;
    quasi_newton_par.max_steps = local_steps;
    VINA_U_FOR(step, global_steps) {
        // if(increment_me)
        // 	++(*increment_me);
        if ((max_evals > 0) & (evalcount > max_evals)) break;
        output_type candidate = tmp;
        mutate_conf(candidate.c, m, mutation_amplitude, generator);
        quasi_newton_par(m, p, ig, candidate, g, hunt_cap, evalcount);
        if (step == 0 || metropolis_accept(tmp.e, candidate.e, temperature, generator)) {
            tmp = candidate;

            m.set(tmp.c);  // FIXME? useless?

            // FIXME only for very promising ones
            if (tmp.e < best_e || out.size() < num_saved_mins) {
                quasi_newton_par(m, p, ig, tmp, g, authentic_v, evalcount);
                m.set(tmp.c);  // FIXME? useless?
                tmp.coords = m.get_heavy_atom_movable_coords();
                add_to_output_container(out, tmp, min_rmsd,
                                        num_saved_mins);  // 20 - max size
                if (tmp.e < best_e) best_e = tmp.e;
            }
        }
    }
    VINA_CHECK(!out.empty());
    VINA_CHECK(out.front().e <= out.back().e);  // make sure the sorting worked in the correct order
                                      }
template <typename Config>
__host__ void monte_carlo_template::run_search<Config>(std::vector<model> &m_gpu, std::vector<output_container> &out_gpu,
    std::vector<precalculate_byatom> &p_gpu, triangular_matrix_cuda_t *m_data_list_gpu,
    const igrid &ig, const vec &corner1, const vec &corner2, rng &generator, int verbosity,
    unsigned long long seed) const {
    /* Definitions from vina1.2 */
    DEBUG_PRINTF("entering CUDA monte_carlo search\n");  // debug
    // DEBUG_PRINTF("out[0].coords.size:%d",out_gpu[0][0].coords.size());
    vec authentic_v(1000, 1000,
                    1000);  // FIXME? this is here to avoid max_fl/max_fl

    quasi_newton quasi_newton_par;
    const int quasi_newton_par_max_steps = local_steps;  // no need to decrease step

    /* Allocate CPU memory and define new data structure */
    DEBUG_PRINTF("Allocating CPU memory\n");  // debug
    m_cuda_t_<Config> *m_cuda;
    checkCUDA(cudaMallocHost(&m_cuda, sizeof(m_cuda_t_<Config>)));

    output_type_cuda_t_<Config> *rand_molec_struc_tmp;
    checkCUDA(cudaMallocHost(&rand_molec_struc_tmp, sizeof(output_type_cuda_t_<Config>)));

    ig_cuda_t_<Config> *ig_cuda_ptr;
    checkCUDA(cudaMallocHost(&ig_cuda_ptr, sizeof(ig_cuda_t_<Config>)));

    p_cuda_t_cpu_<Config> *p_cuda;
    checkCUDA(cudaMallocHost(&p_cuda, sizeof(p_cuda_t_cpu_<Config>)));

    /* End CPU allocation */

    /* Allocate GPU memory */
    DEBUG_PRINTF("Allocating GPU memory\n");
    size_t m_cuda_size = sizeof(m_cuda_t_<Config>);
    DEBUG_PRINTF("m_cuda_size=%lu\n", m_cuda_size);
    size_t ig_cuda_size = sizeof(ig_cuda_t_<Config>);
    DEBUG_PRINTF("ig_cuda_size=%lu\n", ig_cuda_size);
    DEBUG_PRINTF("p_cuda_size_cpu=%lu\n", sizeof(p_cuda_t_cpu));

    size_t p_cuda_size_gpu = sizeof(p_cuda_t_<Config>);
    DEBUG_PRINTF("p_cuda_size_gpu=%lu\n", p_cuda_size_gpu);

    static_assert(Config::SIZE_OF_MOLEC_STRUC_ % sizeof(float) == 0,
                  "SIZE_OF_MOLEC_STRUC_ must be expressed in bytes");
    // rand_molec_struc_gpu
    CudaMemory<float> rand_molec_struc_gpu(
        thread * Config::SIZE_OF_MOLEC_STRUC_ / sizeof(float));
    float epsilon_fl_float = static_cast<float>(epsilon_fl);

    // use cuRand to generate random values on GPU
    CudaMemory<curandStatePhilox4_32_10_t> states(thread);
    DEBUG_PRINTF("random states size=%lu\n",
                 sizeof(curandStatePhilox4_32_10_t) * thread);

    // hunt_cap_gpu
    CudaMemory<float> hunt_cap_gpu(3);
    float hunt_cap_float[3] = {static_cast<float>(hunt_cap[0]), static_cast<float>(hunt_cap[1]),
                               static_cast<float>(hunt_cap[2])};

    // Preparing m related data
    CudaMemory<m_cuda_t_<Config>> m_cuda_gpu(num_of_ligands);
    DEBUG_PRINTF("m_cuda_size=%lu", m_cuda_size);
    // Preparing p related data

    CudaMemory<p_cuda_t_<Config>> p_cuda_gpu(num_of_ligands);
    DEBUG_PRINTF("p_cuda_gpu=%p\n", p_cuda_gpu.get());
    // Preparing ig related data (cache related data)
    float authentic_v_float[3]
        = {static_cast<float>(authentic_v[0]), static_cast<float>(authentic_v[1]),
           static_cast<float>(authentic_v[2])};

    CudaMemory<float> authentic_v_gpu(sizeof(authentic_v_float) / sizeof(float));
    // Preparing result data
    CudaMemory<output_type_cuda_t_<Config>> results_gpu(thread);

    CudaMemory<m_cuda_t_<Config>> m_cuda_global(thread);

    CudaMemory<matrix_d_<Config>> h_cuda_global(thread);

    /* End Allocating GPU Memory */

    assert(num_of_ligands <= DefaultConfig::MAX_LIGAND_NUM);
    assert(thread <= DefaultConfig::MAX_THREAD);

    struct tmp_struct {
        int start_index = 0;
        int parent_index = 0;
        void store_node(tree<segment> &child_ptr, rigid_cuda_t_<Config> &rigid) {
            start_index++;  // start with index 1, index 0 is root node
            rigid.parent[start_index] = parent_index;
            rigid.atom_range[start_index][0] = child_ptr.node.begin;
            rigid.atom_range[start_index][1] = child_ptr.node.end;
            for (int i = 0; i < 9; i++)
                rigid.orientation_m[start_index][i] = child_ptr.node.get_orientation_m().data[i];
            rigid.orientation_q[start_index][0] = child_ptr.node.orientation().R_component_1();
            rigid.orientation_q[start_index][1] = child_ptr.node.orientation().R_component_2();
            rigid.orientation_q[start_index][2] = child_ptr.node.orientation().R_component_3();
            rigid.orientation_q[start_index][3] = child_ptr.node.orientation().R_component_4();
            for (int i = 0; i < 3; i++) {
                rigid.origin[start_index][i] = child_ptr.node.get_origin()[i];
                rigid.axis[start_index][i] = child_ptr.node.get_axis()[i];
                rigid.relative_axis[start_index][i] = child_ptr.node.relative_axis[i];
                rigid.relative_origin[start_index][i] = child_ptr.node.relative_origin[i];
            }
            if (child_ptr.children.size() == 0)
                return;
            else {
                assert(start_index < DefaultConfig::MAX_NUM_OF_RIGID);
                int parent_index_tmp = start_index;
                for (int i = 0; i < child_ptr.children.size(); i++) {
                    this->parent_index = parent_index_tmp;  // Update parent index
                    this->store_node(child_ptr.children[i], rigid);
                }
            }
        }
    };
    

    for (int l = 0; l < num_of_ligands; ++l) {
        DEBUG_PRINTF("total num_of_ligands:%d\n",num_of_ligands);
        DEBUG_PRINTF("num_of_ligands:%d\n",l);
        model &m = m_gpu[l];
        const precalculate_byatom &p = p_gpu[l];

        /* Prepare m related data */
        conf_size s = m.get_size();
        change g(s);
        output_type tmp(s, 0);
        tmp.c = m.get_initial_conf();

        assert(m.atoms.size() < DefaultConfig::MAX_NUM_OF_ATOMS);

        // Preparing ligand data
        DEBUG_PRINTF("prepare ligand data\n");
        assert(m.num_other_pairs() == 0);  // m.other_pairs is not supported!
        assert(m.ligands.size() <= 1);     // Only one ligand supported!

        if (m.ligands.size() == 0) {  // ligand parsing error
            m_cuda->m_num_movable_atoms = -1;
            DEBUG_PRINTF("copy m_cuda to gpu, size=%lu\n", sizeof(m_cuda_t_<Config>));
            checkCUDA(cudaMemcpy(m_cuda_gpu + l, m_cuda, sizeof(m_cuda_t_<Config>), cudaMemcpyHostToDevice));
        } else {
            for (int i = 0; i < m.atoms.size(); i++) {
                m_cuda->atoms[i].types[0]
                    = m.atoms[i].el;  // To store 4 atoms types (el, ad, xs, sy)
                m_cuda->atoms[i].types[1] = m.atoms[i].ad;
                m_cuda->atoms[i].types[2] = m.atoms[i].xs;
                m_cuda->atoms[i].types[3] = m.atoms[i].sy;
                for (int j = 0; j < 3; j++) {
                    m_cuda->atoms[i].coords[j] = m.atoms[i].coords[j];  // To store atom coords
                }
            }

            // To store atoms coords
            for (int i = 0; i < m.coords.size(); i++) {
                for (int j = 0; j < 3; j++) {
                    m_cuda->m_coords.coords[i][j] = m.coords[i].data[j];
                }
            }

            // To store minus forces
            for (int i = 0; i < m.coords.size(); i++) {
                for (int j = 0; j < 3; j++) {
                    m_cuda->minus_forces.coords[i][j] = m.minus_forces[i].data[j];
                }
            }

            m_cuda->ligand.pairs.num_pairs = m.ligands[0].pairs.size();
            for (int i = 0; i < m_cuda->ligand.pairs.num_pairs; i++) {
                m_cuda->ligand.pairs.type_pair_index[i] = m.ligands[0].pairs[i].type_pair_index;
                m_cuda->ligand.pairs.a[i] = m.ligands[0].pairs[i].a;
                m_cuda->ligand.pairs.b[i] = m.ligands[0].pairs[i].b;
            }
            m_cuda->ligand.begin = m.ligands[0].begin;  // 0
            m_cuda->ligand.end = m.ligands[0].end;      // 29
            ligand &m_ligand = m.ligands[0];            // Only support one ligand
            DEBUG_PRINTF("m_ligand.end=%lu, DefaultConfig::MAX_NUM_OF_ATOMS=%d\n", m_ligand.end, Config::MAX_NUM_OF_ATOMS_);
            assert(m_ligand.end < Config::MAX_NUM_OF_ATOMS_);

            // Store root node
            m_cuda->ligand.rigid.atom_range[0][0] = m_ligand.node.begin;
            m_cuda->ligand.rigid.atom_range[0][1] = m_ligand.node.end;
            for (int i = 0; i < 3; i++)
                m_cuda->ligand.rigid.origin[0][i] = m_ligand.node.get_origin()[i];
            for (int i = 0; i < 9; i++)
                m_cuda->ligand.rigid.orientation_m[0][i]
                    = m_ligand.node.get_orientation_m().data[i];
            m_cuda->ligand.rigid.orientation_q[0][0] = m_ligand.node.orientation().R_component_1();
            m_cuda->ligand.rigid.orientation_q[0][1] = m_ligand.node.orientation().R_component_2();
            m_cuda->ligand.rigid.orientation_q[0][2] = m_ligand.node.orientation().R_component_3();
            m_cuda->ligand.rigid.orientation_q[0][3] = m_ligand.node.orientation().R_component_4();
            for (int i = 0; i < 3; i++) {
                m_cuda->ligand.rigid.axis[0][i] = 0;
                m_cuda->ligand.rigid.relative_axis[0][i] = 0;
                m_cuda->ligand.rigid.relative_origin[0][i] = 0;
            }

            // Store children nodes (in depth-first order)
            DEBUG_PRINTF("store children nodes\n");

            tmp_struct ts;
            
            for (int i = 0; i < m_ligand.children.size(); i++) {
                ts.parent_index = 0;  // Start a new branch, whose parent is 0
                ts.store_node(m_ligand.children[i], m_cuda->ligand.rigid);
            }
            m_cuda->ligand.rigid.num_children = ts.start_index;
            // set children map
            DEBUG_PRINTF("set children map\n");
            for (int i=0;i< Config::MAX_NUM_OF_RIGID_;i++){
                DEBUG_PRINTF("m_cuda->ligand.rigid.parent[%d]=%d\n",i,m_cuda->ligand.rigid.parent[i]);
            }
            for (int i = 0; i < Config::MAX_NUM_OF_RIGID_; i++)
                for (int j = 0; j < Config::MAX_NUM_OF_RIGID_; j++) {
                    m_cuda->ligand.rigid.children_map[i][j] = false;
                    m_cuda->ligand.rigid.descendant_map[i][j] = false;
                }

            for (int i = Config::MAX_NUM_OF_RIGID_ - 1; i >= 0; i--) {
                if (i > 0) {
                    m_cuda->ligand.rigid.children_map[m_cuda->ligand.rigid.parent[i]][i] = true;
                    m_cuda->ligand.rigid.descendant_map[m_cuda->ligand.rigid.parent[i]][i] = true;
                }
                for (int j = i + 1; j < Config::MAX_NUM_OF_RIGID_; j++) {
                    if (m_cuda->ligand.rigid.descendant_map[i][j])
                        m_cuda->ligand.rigid.descendant_map[m_cuda->ligand.rigid.parent[i]][j]
                            = true;
                }
            }
            m_cuda->m_num_movable_atoms = m.num_movable_atoms();

            DEBUG_PRINTF("copy m_cuda to gpu, size=%lu\n", sizeof(m_cuda_t_<Config>));
            checkCUDA(cudaMemcpy(m_cuda_gpu + l, m_cuda, sizeof(m_cuda_t_<Config>), cudaMemcpyHostToDevice));

            /* Prepare rand_molec_struc data */
            int lig_torsion_size = tmp.c.ligands[0].torsions.size();
            DEBUG_PRINTF("lig_torsion_size=%d\n", lig_torsion_size);
            int flex_torsion_size;
            if (tmp.c.flex.size() != 0)
                flex_torsion_size = tmp.c.flex[0].torsions.size();
            else
                flex_torsion_size = 0;
            // std::vector<vec> uniform_data;
            // uniform_data.resize(thread);

            for (int i = 0; i < threads_per_ligand; ++i) {
                if (!local_only) {
                    tmp.c.randomize(corner1, corner2,
                                    generator);  // generate a random structure,
                                                 // can move to GPU if necessary
                }
                for (int j = 0; j < 3; j++)
                    rand_molec_struc_tmp->position[j] = tmp.c.ligands[0].rigid.position[j];
                assert(lig_torsion_size <= Config::MAX_NUM_OF_LIG_TORSION_);
                for (int j = 0; j < lig_torsion_size; j++)
                    rand_molec_struc_tmp->lig_torsion[j]
                        = tmp.c.ligands[0].torsions[j];  // Only support one ligand
                assert(flex_torsion_size <= Config::MAX_NUM_OF_FLEX_TORSION_);
                for (int j = 0; j < flex_torsion_size; j++)
                    rand_molec_struc_tmp->flex_torsion[j]
                        = tmp.c.flex[0].torsions[j];  // Only support one flex

                rand_molec_struc_tmp->orientation[0]
                    = (float)tmp.c.ligands[0].rigid.orientation.R_component_1();
                rand_molec_struc_tmp->orientation[1]
                    = (float)tmp.c.ligands[0].rigid.orientation.R_component_2();
                rand_molec_struc_tmp->orientation[2]
                    = (float)tmp.c.ligands[0].rigid.orientation.R_component_3();
                rand_molec_struc_tmp->orientation[3]
                    = (float)tmp.c.ligands[0].rigid.orientation.R_component_4();

                rand_molec_struc_tmp->lig_torsion_size = lig_torsion_size;

                float *rand_molec_struc_gpu_tmp
                    = rand_molec_struc_gpu
                      + (l * threads_per_ligand + i) * Config::SIZE_OF_MOLEC_STRUC_ / sizeof(float);
                checkCUDA(cudaMemcpy(rand_molec_struc_gpu_tmp, rand_molec_struc_tmp,
                                     Config::SIZE_OF_MOLEC_STRUC_, cudaMemcpyHostToDevice));
            }

            /* Preparing p related data */
            DEBUG_PRINTF("Preaparing p related data\n");  // debug

            // copy pointer instead of data
            p_cuda->m_cutoff_sqr = p.m_cutoff_sqr;
            p_cuda->factor = p.m_factor;
            p_cuda->n = p.m_n;
            p_cuda->m_data_size = p.m_data.m_data.size();
            checkCUDA(cudaMemcpy(p_cuda_gpu + l, p_cuda, sizeof(p_cuda_t_<Config>), cudaMemcpyHostToDevice));
            checkCUDA(cudaMemcpy(&(p_cuda_gpu[l].m_data), &(m_data_list_gpu[l].p_data),
                                 sizeof(p_m_data_cuda_t_<Config> *),
                                 cudaMemcpyHostToDevice));  // check if fl == float
        }
    }

    /* Prepare data only concerns rigid receptor */

    // Preparing igrid related data
    DEBUG_PRINTF("Preparing ig related data\n");  // debug

    ig_cuda_ptr->atu = ig.get_atu();  // atu
    DEBUG_PRINTF("ig_cuda_ptr->atu=%d\n", ig_cuda_ptr->atu);
    ig_cuda_ptr->slope = ig.get_slope();  // slope
    std::vector<grid> tmp_grids = ig.get_grids();
    int grid_size = tmp_grids.size();
    DEBUG_PRINTF("ig.size()=%d, DefaultConfig::GRIDS_SIZE=%d, should be 33\n", grid_size, SmallConfig::GRIDS_SIZE_);

    for (int i = 0; i < grid_size; i++) {
        // DEBUG_PRINTF("i=%d\n",i); //debug
        for (int j = 0; j < 3; j++) {
            ig_cuda_ptr->grids[i].m_init[j] = tmp_grids[i].m_init[j];
            ig_cuda_ptr->grids[i].m_factor[j] = tmp_grids[i].m_factor[j];
            ig_cuda_ptr->grids[i].m_dim_fl_minus_1[j] = tmp_grids[i].m_dim_fl_minus_1[j];
            ig_cuda_ptr->grids[i].m_factor_inv[j] = tmp_grids[i].m_factor_inv[j];
        }
        if (tmp_grids[i].m_data.dim0() != 0) {
            ig_cuda_ptr->grids[i].m_i = tmp_grids[i].m_data.dim0();
            assert(DefaultConfig::MAX_NUM_OF_GRID_MI >= ig_cuda_ptr->grids[i].m_i);
            ig_cuda_ptr->grids[i].m_j = tmp_grids[i].m_data.dim1();
            assert(DefaultConfig::MAX_NUM_OF_GRID_MJ >= ig_cuda_ptr->grids[i].m_j);
            ig_cuda_ptr->grids[i].m_k = tmp_grids[i].m_data.dim2();
            assert(DefaultConfig::MAX_NUM_OF_GRID_MK >= ig_cuda_ptr->grids[i].m_k);

            assert(tmp_grids[i].m_data.m_data.size()
                   == ig_cuda_ptr->grids[i].m_i * ig_cuda_ptr->grids[i].m_j
                              * ig_cuda_ptr->grids[i].m_k);
            memcpy(ig_cuda_ptr->grids[i].m_data, tmp_grids[i].m_data.m_data.data(),
                   tmp_grids[i].m_data.m_data.size() * sizeof(fl));
        } else {
            ig_cuda_ptr->grids[i].m_i = 0;
            ig_cuda_ptr->grids[i].m_j = 0;
            ig_cuda_ptr->grids[i].m_k = 0;
    }
    }
    DEBUG_PRINTF("memcpy ig_cuda, ig_cuda_size=%lu\n", ig_cuda_size);
    CudaMemory<ig_cuda_t_<Config>> ig_cuda_gpu(1);
    checkCUDA(cudaMemcpy(ig_cuda_gpu.get(), ig_cuda_ptr, ig_cuda_size,
                         cudaMemcpyHostToDevice));

    float mutation_amplitude_float = static_cast<float>(mutation_amplitude);

    checkCUDA(cudaMemcpy(hunt_cap_gpu, hunt_cap_float, 3 * sizeof(float), cudaMemcpyHostToDevice));
    float hunt_test[3];
    checkCUDA(cudaMemcpy(hunt_test, hunt_cap_gpu, 3 * sizeof(float), cudaMemcpyDeviceToHost));
    DEBUG_PRINTF("hunt_test[1]=%f, hunt_cap_float[1]=%f\n", hunt_test[1], hunt_cap_float[1]);
    checkCUDA(cudaMemcpy(authentic_v_gpu, authentic_v_float, sizeof(authentic_v_float),
                         cudaMemcpyHostToDevice));

    /* Add timing */
    cudaEvent_t start, stop;
    checkCUDA(cudaEventCreate(&start));
    checkCUDA(cudaEventCreate(&stop));
    checkCUDA(cudaEventRecord(start, NULL));

    /* Launch kernel */
    DEBUG_PRINTF("launch kernel, global_steps=%d, thread=%d, num_of_ligands=%d\n", global_steps,
                 thread, num_of_ligands);

    CudaMemory<output_type_cuda_t_<Config>> results_aux(5 * thread);
    CudaMemory<change_cuda_t_<Config>> change_aux(6 * thread);
    CudaMemory<pot_cuda_t_<Config>> pot_aux(thread);

    kernel<Config, 32><<<(thread + 3) / 4, 128>>>(m_cuda_gpu, ig_cuda_gpu, p_cuda_gpu, rand_molec_struc_gpu,
                               quasi_newton_par_max_steps, mutation_amplitude_float, states, seed,
                               epsilon_fl_float, hunt_cap_gpu, authentic_v_gpu, results_gpu,
                               results_aux, change_aux, pot_aux, h_cuda_global, m_cuda_global,
                               global_steps, num_of_ligands, threads_per_ligand);
    checkCUDA(cudaDeviceSynchronize());
    // Device to Host memcpy of precalculated_byatom, copy back data to p_gpu
    p_m_data_cuda_t_<Config> *p_data;
    checkCUDA(cudaMallocHost(&p_data, sizeof(p_m_data_cuda_t_<Config>) * Config::MAX_P_DATA_M_DATA_SIZE_));
    output_type_cuda_t_<Config> *results;
    checkCUDA(cudaMallocHost(&results, thread * sizeof(output_type_cuda_t_<Config>)));

    for (int l = 0; l < num_of_ligands; ++l) {
    // copy data to m_data on CPU, then to p_gpu[l]
    int pnum = p_gpu[l].m_data.m_data.size();
    checkCUDA(cudaMemcpy(p_data, m_data_list_gpu[l].p_data, sizeof(p_m_data_cuda_t_<Config>) * pnum,
                             cudaMemcpyDeviceToHost));
    checkCUDA(cudaFree(m_data_list_gpu[l].p_data));  // free m_cuda pointers in p_cuda
    for (int i = 0; i < pnum; ++i) {
            memcpy(&p_gpu[l].m_data.m_data[i].fast[0], p_data[i].fast, sizeof(p_data[i].fast));
            memcpy(&p_gpu[l].m_data.m_data[i].smooth[0], p_data[i].smooth,
                   sizeof(p_data[i].smooth));
    }
    }

    checkCUDA(cudaDeviceSynchronize());
    /* Timing output */

    checkCUDA(cudaEventRecord(stop, NULL));
    cudaEventSynchronize(stop);
    float msecTotal = 0.0f;
    cudaEventElapsedTime(&msecTotal, start, stop);
    printf("Time spend on GPU is %f ms\n", msecTotal);

    /* Convert result data. Can be improved by mapping memory
     */
    DEBUG_PRINTF("cuda to vina\n");

    checkCUDA(cudaMemcpy(results, results_gpu, thread * sizeof(output_type_cuda_t_<Config>),
                         cudaMemcpyDeviceToHost));

    std::vector<output_type> result_vina = cuda_to_vina<Config>(results, thread);

    DEBUG_PRINTF("result size=%lu\n", result_vina.size());

    for (int i = 0; i < thread; ++i) {
    add_to_output_container(out_gpu[i / threads_per_ligand], result_vina[i], min_rmsd,
                                num_saved_mins);
    }
    for (int i = 0; i < num_of_ligands; ++i) {
    DEBUG_PRINTF("output poses size = %lu\n", out_gpu[i].size());
    if (out_gpu[i].size() == 0) continue;
    DEBUG_PRINTF("output poses energy from gpu =");
    for (int j = 0; j < out_gpu[i].size(); ++j) DEBUG_PRINTF("%f ", out_gpu[i][j].e);
    DEBUG_PRINTF("\n");
    }

    /* Free pinned host memory */
    checkCUDA(cudaFreeHost(m_cuda));
    checkCUDA(cudaFreeHost(rand_molec_struc_tmp));
    checkCUDA(cudaFreeHost(ig_cuda_ptr));
    checkCUDA(cudaFreeHost(p_cuda));
    checkCUDA(cudaFreeHost(p_data));
    checkCUDA(cudaFreeHost(results));

    DEBUG_PRINTF("exit monte_carlo\n");
    }
template <>
__host__ void monte_carlo_template::do_docking<SmallConfig>(std::vector<model> &m_gpu, std::vector<output_container> &out_gpu,
    std::vector<precalculate_byatom> &p_gpu, triangular_matrix_cuda_t *m_data_list_gpu,
    const igrid &ig, const vec &corner1, const vec &corner2, rng &generator, int verbosity,
    unsigned long long seed) const {
        monte_carlo_template::run_search<SmallConfig>(m_gpu, out_gpu,p_gpu, m_data_list_gpu,ig, corner1, corner2, generator, verbosity,seed);
}
template <>
__host__ void monte_carlo_template::do_docking<MediumConfig>(std::vector<model> &m_gpu, std::vector<output_container> &out_gpu,
    std::vector<precalculate_byatom> &p_gpu, triangular_matrix_cuda_t *m_data_list_gpu,
    const igrid &ig, const vec &corner1, const vec &corner2, rng &generator, int verbosity,
    unsigned long long seed) const {
        monte_carlo_template::run_search<MediumConfig>(m_gpu, out_gpu,p_gpu, m_data_list_gpu,ig, corner1, corner2, generator, verbosity,seed);
}
template <>
__host__ void monte_carlo_template::do_docking<LargeConfig>(std::vector<model> &m_gpu, std::vector<output_container> &out_gpu,
    std::vector<precalculate_byatom> &p_gpu, triangular_matrix_cuda_t *m_data_list_gpu,
    const igrid &ig, const vec &corner1, const vec &corner2, rng &generator, int verbosity,
    unsigned long long seed) const {
        monte_carlo_template::run_search<LargeConfig>(m_gpu, out_gpu,p_gpu, m_data_list_gpu,ig, corner1, corner2, generator, verbosity,seed);
}
template <>
__host__ void monte_carlo_template::do_docking<ExtraLargeConfig>(std::vector<model> &m_gpu, std::vector<output_container> &out_gpu,
    std::vector<precalculate_byatom> &p_gpu, triangular_matrix_cuda_t *m_data_list_gpu,
    const igrid &ig, const vec &corner1, const vec &corner2, rng &generator, int verbosity,
    unsigned long long seed) const {
        monte_carlo_template::run_search<ExtraLargeConfig>(m_gpu, out_gpu,p_gpu, m_data_list_gpu,ig, corner1, corner2, generator, verbosity,seed);
}
template <>
__host__ void monte_carlo_template::do_docking<MaxConfig>(std::vector<model> &m_gpu, std::vector<output_container> &out_gpu,
    std::vector<precalculate_byatom> &p_gpu, triangular_matrix_cuda_t *m_data_list_gpu,
    const igrid &ig, const vec &corner1, const vec &corner2, rng &generator, int verbosity,
    unsigned long long seed) const {
        monte_carlo_template::run_search<MaxConfig>(m_gpu, out_gpu,p_gpu, m_data_list_gpu,ig, corner1, corner2, generator, verbosity,seed);
}
/* Above based on monte-carlo.cpp */

// #endif
