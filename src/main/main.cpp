#include <cstddef>
#include <iostream>
#include <string>
#include <vector>  // ligand paths
#include <boost/program_options.hpp>
#include "vina.h"
#include "utils.h"
#include "scoring_function.h"

#include <cuda_runtime.h>
#include <device_launch_parameters.h>
#include "types.cuh"

struct usage_error : public std::runtime_error {
    usage_error(const std::string& message) : std::runtime_error(message) {}
};

struct options_occurrence {
    bool some;
    bool all;
    options_occurrence() : some(false), all(true) {}  // convenience
    options_occurrence& operator+=(const options_occurrence& x) {
        some = some || x.some;
        all = all && x.all;
        return *this;
    }
};

options_occurrence get_occurrence(boost::program_options::variables_map& vm,
                                  boost::program_options::options_description& d) {
    options_occurrence tmp;
    VINA_FOR_IN(i, d.options())
    if (vm.count((*d.options()[i]).long_name()))
        tmp.some = true;
    else
        tmp.all = false;
    return tmp;
}

void check_occurrence(boost::program_options::variables_map& vm,
                      boost::program_options::options_description& d) {
    VINA_FOR_IN(i, d.options()) {
        const std::string& str = (*d.options()[i]).long_name();
        if (!vm.count(str)) std::cerr << "Required parameter --" << str << " is missing!\n";
    }
}
struct Ligand {
    int num_atoms;
    int num_torsions;
    int num_rigids;
    int num_lig_pairs;
    int index; 
    float score; 
};

const float WEIGHT_ATOMS = 128.0/300.0;
const float WEIGHT_TORSIONS = 128.0/48;
const float WEIGHT_RIGIDS = 1.0;
const float WEIGHT_LIG_PAIRS = 1.0/32;


float calculateScore(const Ligand &ligand) {
    return ligand.num_atoms * WEIGHT_ATOMS + 
           ligand.num_torsions * WEIGHT_TORSIONS +
           ligand.num_rigids * WEIGHT_RIGIDS +
           ligand.num_lig_pairs * WEIGHT_LIG_PAIRS;
}
bool compareLigands(const Ligand &a, const Ligand &b) {
    return a.score < b.score;
}
void classifyLigands(const std::vector<Ligand>& ligands,std::vector<Ligand> &smallGroup, std::vector<Ligand> &mediumGroup,std::vector<Ligand> & largeGroup, std::vector<Ligand> &extraLargeGroup, std::vector<Ligand> &overflowGroup) {
    const int atomThresholds[5] = {40, 80, 120, 160, 300};
    const int torsionThresholds[5] = {8, 16, 24, 36, 48};
    const int rigidThresholds[5] = {12, 24, 36, 64, 128};
    const int pairThresholds[5] = {300, 600, 1024, 2048, 4096};

    for (const auto& lig : ligands) {
        if (lig.num_atoms <= atomThresholds[0] && lig.num_torsions <= torsionThresholds[0] &&
            lig.num_rigids <= rigidThresholds[0] && lig.num_lig_pairs <= pairThresholds[0]) {
            smallGroup.push_back(lig);
        } else if (lig.num_atoms <= atomThresholds[1] && lig.num_torsions <= torsionThresholds[1] &&
                   lig.num_rigids <= rigidThresholds[1] && lig.num_lig_pairs <= pairThresholds[1]) {
            mediumGroup.push_back(lig);
        } else if (lig.num_atoms <= atomThresholds[2] && lig.num_torsions <= torsionThresholds[2] &&
                   lig.num_rigids <= rigidThresholds[2] && lig.num_lig_pairs <= pairThresholds[2]) {
            largeGroup.push_back(lig);
        } else if (lig.num_atoms <= atomThresholds[3] && lig.num_torsions <= torsionThresholds[3] &&
                   lig.num_rigids <= rigidThresholds[3] && lig.num_lig_pairs <= pairThresholds[3]) {
            extraLargeGroup.push_back(lig);
        } else {
            overflowGroup.push_back(lig);
        }
    }
}
void printMaxValues(const std::vector<Ligand>& group) {
    int max_atoms = std::numeric_limits<int>::min();
    int max_torsions = std::numeric_limits<int>::min();
    int max_rigids = std::numeric_limits<int>::min();
    int max_lig_pairs = std::numeric_limits<int>::min();

    for (const auto& ligand : group) {
        max_atoms = std::max(max_atoms, ligand.num_atoms);
        max_torsions = std::max(max_torsions, ligand.num_torsions);
        max_rigids = std::max(max_rigids, ligand.num_rigids);
        max_lig_pairs = std::max(max_lig_pairs, ligand.num_lig_pairs);
    }

    std::cout << "Max num_atoms: " << max_atoms <<" Max num_torsions: " << max_torsions;
    std::cout << " Max num_rigids: " << max_rigids;
    std::cout << " Max num_lig_pairs: " << max_lig_pairs << std::endl;
    std::cout << "Group size: "<< group.size()<<std::endl;
}

template<typename Config>
int predict_peak_memory(int batch_size, int exhaustiveness, int all_atom2_numbers) {
    int64_t gpu_memory = 0;
    // precalculate
    gpu_memory += (int64_t)(1) * all_atom2_numbers * sizeof(precalculate_element_cuda_t_<Config>);

    // m_cuda_gpu
    gpu_memory += (int64_t)(1) * batch_size * sizeof(m_cuda_t_<Config>);
    // ig_cuda_gpu
    gpu_memory += sizeof(ig_cuda_t_<Config>);
    // p_cuda_gpu
    gpu_memory += (int64_t)(1) * batch_size * sizeof(p_cuda_t_<Config>);
    // rand_molec_struc_gpu
    gpu_memory += (int64_t)(1) * batch_size * exhaustiveness * Config::SIZE_OF_MOLEC_STRUC_;
    // results_gpu
    gpu_memory += (int64_t)(1) * batch_size * exhaustiveness * sizeof(output_type_cuda_t_<Config>);
    // m_cuda_global
    gpu_memory += (int64_t)(1) * batch_size * exhaustiveness * sizeof(m_cuda_t_<Config>);
    // h_cuda_global
    gpu_memory += (int64_t)(1) * batch_size * exhaustiveness * sizeof(matrix_d_<Config>);
    // change_aux
    gpu_memory += (int64_t)(6) * batch_size * exhaustiveness * sizeof(change_cuda_t_<Config>);
    // results_aux
    gpu_memory += (int64_t)(5) * batch_size * exhaustiveness * sizeof(output_type_cuda_t_<Config>);
    // pot_aux
    gpu_memory += (int64_t)(1) * batch_size * exhaustiveness * sizeof(pot_cuda_t_<Config>);
    // states
    gpu_memory
        += (int64_t)(1) * batch_size * exhaustiveness * 64;  // sizeof(curandStatePhilox4_32_10_t)

    return gpu_memory / (1024 * 1024);
}

int predict_peak_memory(int batch_size, int exhaustiveness, int all_atom2_numbers) {
    return predict_peak_memory<DefaultConfig>(batch_size, exhaustiveness, all_atom2_numbers);
}


typedef std::pair<std::string, model> named_model;
std::vector<std::string> gpu_out_name;

template<typename Config>
void template_batch_docking(Vina &v,std::vector<named_model> &all_ligands,std::vector<Ligand> &sized_group,
                        std::string batch_type,int exhaustiveness,float max_memory,
                        float receptor_atom_numbers,std::string out_dir,int num_modes,
                        double min_rmsd,int max_evals,int max_step,int seed, int refine_step,bool local_only,
                        double energy_range){
    int processed_ligands = 0;
    int batch_id = 0 ;
    while (processed_ligands < sized_group.size()) {
        std::cout << batch_type<< std::endl;
        printMaxValues(sized_group);
        ++batch_id;
        auto start = std::chrono::system_clock::now();
        Vina v1(v);  // reuse init'ed maps
        int batch_size = 0;
        int all_atom2_numbers = 0;         // total number of atom^2 in current batch
        std::vector<model> batch_ligands;  // ligands in current batch
        while (predict_peak_memory<Config>(batch_size, exhaustiveness, all_atom2_numbers)
                    < max_memory
                && processed_ligands + batch_size < sized_group.size()) {
            batch_ligands.emplace_back(
                all_ligands[sized_group[processed_ligands + batch_size].index].second);
            int next_atom_numbers
                = batch_ligands.back().get_atoms().size() + receptor_atom_numbers;
            int next_atom2_numbers
                = next_atom_numbers * next_atom_numbers;  // Memory ~ atom numbers^2
            all_atom2_numbers += next_atom2_numbers;
            batch_size++;
        }
        DEBUG_PRINTF("batch size=%d, all_atom2_numbers=%d\n", batch_size,
                        all_atom2_numbers);

        std::cout <<batch_type<< " Batch " << batch_id << " size: " << batch_size << std::endl;
        std::vector<std::string> batch_ligand_names;
        for (int i = processed_ligands; i < processed_ligands + batch_size; i++) {
            batch_ligand_names.push_back(all_ligands[sized_group[i].index].first);
        }
        processed_ligands += batch_size;
        gpu_out_name = {};
        VINA_RANGE(i, 0, batch_ligand_names.size()) {
            gpu_out_name.push_back(
                default_output(get_filename(batch_ligand_names[i]), out_dir));
        }
        v1.set_ligand_from_object_gpu(batch_ligands);
        
        v1.global_search_gpu<Config>(exhaustiveness, num_modes, min_rmsd, max_evals, max_step,
                                batch_ligand_names.size(), (unsigned long long)seed,
                                refine_step, local_only);
                                
        
        
        v1.write_poses_gpu(gpu_out_name, num_modes, energy_range);
        auto end = std::chrono::system_clock::now();
        std::cout << "Batch " << batch_id << " running time: "
                    << std::chrono::duration_cast<std::chrono::milliseconds>(end - start)
                            .count()
                    << "ms" << std::endl;
    }
    }
int main(int argc, char* argv[]) {
    using namespace boost::program_options;
    const std::string git_version = VERSION;
    const std::string version_string = "UniDock-Pro " + git_version;

    const char* udp_logo = R"UDP(
 _   _     ____     _____ 
| | | |   |  _ \   |  __ \ 
| |_| |   | | | |  | |__) |
|  _  |   | |_| |  |  ___/
|_| |_|   |____/   |_|    

    U        D        P
)UDP";
    const std::string error_message
        = "\n\n\
Please report bugs through the Issue Tracker on GitHub \n\
(https://github.com/UniDock-Pro/UniDock-Pro/issues)., so\n\
that this problem can be resolved. The reproducibility of the\n\
error may be vital, so please remember to include the following in\n\
your problem report:\n\
* the EXACT error message,\n\
* your version of the program,\n\
* the type of computer system you are running it on,\n\
* all command line options,\n\
* configuration file (if used),\n\
* ligand file as PDBQT,\n\
* receptor file as PDBQT,\n\
* flexible side chains file as PDBQT (if used),\n\
* output file as PDBQT (if any),\n\
* input (if possible),\n\
* random seed the program used (this is printed when the program starts).\n\
\n\
Thank you!\n";

    const std::string cite_message
        = "\n\
If you used UniDock-Pro in your work, please cite:               \n \
\n\
Yu, Y., Cai, C., Wang, J., Bo, Z., Zhu, Z., & Zheng, H. (2023). \n\
UniDock-Pro: GPU-Accelerated Docking Enables Ultralarge Virtual Screening. \n\
Journal of Chemical Theory and Computation.                    \n\
https://doi.org/10.1021/acs.jctc.2c01145                       \n\
\n\
Tang, S., Chen, R., Lin, M., Lin, Q., Zhu, Y., Ding, J., ... & Wu, J. (2022). \n\
Accelerating autodock vina with gpus. Molecules, 27(9), 3041. \n\
DOI 10.3390/molecules27093041                                 \n\
\n\
J. Eberhardt, D. Santos-Martins, A. F. Tillack, and S. Forli  \n\
AutoDock Vina 1.2.0: New Docking Methods, Expanded Force      \n\
Field, and Python Bindings, J. Chem. Inf. Model. (2021)       \n\
DOI 10.1021/acs.jcim.1c00203                                  \n\
\n\
O. Trott, A. J. Olson,                                        \n\
AutoDock Vina: improving the speed and accuracy of docking    \n\
with a new scoring function, efficient optimization and        \n\
multithreading, J. Comp. Chem. (2010)                         \n\
DOI 10.1002/jcc.21334                                         \n\
\n\
Please refer to https://github.com/UniDock-Pro/UniDock-Pro/ for  \n\
bug reporting, license agreements, and more information.      \n";

    try {
        std::string rigid_name;
        std::string flex_name;
        std::string config_name;
        std::string out_name;
        std::vector<std::string> gpu_out_name;
        std::string out_dir;
        std::string out_maps;
        std::vector<std::string> ligand_names;
        std::string ligand_index;  // path to a text file, containing paths to ligands files
        std::vector<std::string> batch_ligand_names;
        std::vector<std::string> gpu_batch_ligand_names;
        // std::vector<std::string> gpu_batch_ligand_names_sdf;
        bool use_sdf_ligand = false;
        std::string maps;
        std::string sf_name = "vina";
        std::string search_mode;
        double center_x;
        double center_y;
        double center_z;
        double size_x;
        double size_y;
        double size_z;
        int seed = 0;
        int exhaustiveness = 8;
        int max_evals = 0;
        int verbosity = 1;
        int num_modes = 9;
        double min_rmsd = 1.0;
        double energy_range = 3.0;
        double grid_spacing = 0.375;
        double buffer_size = 4;
        int max_step = 0;
        int max_gpu_memory = 0;
        int refine_step = 5;

        // vina weights
        double weight_gauss1 = -0.035579;
        double weight_gauss2 = -0.005156;
        double weight_repulsion = 0.840245;
        double weight_hydrophobic = -0.035069;
        double weight_hydrogen = -0.587439;
        double weight_rot = 0.05846;

        // vinardo weights
        double weight_vinardo_gauss1 = -0.045;
        double weight_vinardo_repulsion = 0.8;
        double weight_vinardo_hydrophobic = -0.035;
        double weight_vinardo_hydrogen = -0.600;

        // macrocycle closure
        double weight_glue = 50.000000;  // linear attraction

        bool score_only = false;
        bool local_only = false;
        bool no_refine = false;
        bool force_even_voxels = false;
        bool randomize_only = false;
        bool help = false;
        bool help_advanced = false;
        bool version = false;  // FIXME
        bool autobox = false;
        variables_map vm;

        // sdf
        bool keep_H = true;

        // score only in batch
        std::string score_file("scores.txt");

        positional_options_description positional;  // remains empty
        // GPU Device id to use
        int device_id = 0;

        options_description inputs("Input");
        inputs.add_options()("receptor", value<std::string>(&rigid_name),
                             "rigid part of the receptor (PDBQT or PDB)")(
            "flex", value<std::string>(&flex_name), "flexible side chains, if any (PDBQT or PDB)")(
            "ligand", value<std::vector<std::string> >(&ligand_names)->multitoken(),
            "ligand (PDBQT)")("ligand_index", value<std::string>(&ligand_index),
                              "file containing paths to ligands (PDBQT or SDF")(
            "reference_ligand", value<std::string>(),
            "reference ligand for similarity-guided docking (PDBQT)")(
            "batch", value<std::vector<std::string> >(&batch_ligand_names)->multitoken(),
            "batch ligand (PDBQT)")(
            "gpu_batch", value<std::vector<std::string> >(&gpu_batch_ligand_names)->multitoken(),
            "gpu batch ligand (PDBQT or SDF)")
            // ("gpu_batch_sdf", value< std::vector<std::string>
            // >(&gpu_batch_ligand_names_sdf)->multitoken(), "gpu batch ligand (SDF)")

            ("scoring", value<std::string>(&sf_name)->default_value(sf_name),
             "scoring function (vina or vinardo)");
        // options_description search_area("Search area (required, except with --score_only)");
        options_description search_area("Search space (required)");
        search_area.add_options()(
            "maps", value<std::string>(&maps),
            "affinity maps for the vina scoring function")(
            "center_x", value<double>(&center_x), "X coordinate of the center (Angstrom)")(
            "center_y", value<double>(&center_y), "Y coordinate of the center (Angstrom)")(
            "center_z", value<double>(&center_z), "Z coordinate of the center (Angstrom)")(
            "size_x", value<double>(&size_x), "size in the X dimension (Angstrom)")(
            "size_y", value<double>(&size_y), "size in the Y dimension (Angstrom)")(
            "size_z", value<double>(&size_z), "size in the Z dimension (Angstrom)")(
            "autobox", bool_switch(&autobox),
            "set maps dimensions based on input ligand(s) (for --score_only and --local_only)");
        // options_description outputs("Output prefixes (optional - by default, input names are
        // stripped of .pdbqt\nare used as prefixes. _001.pdbqt, _002.pdbqt, etc. are appended to
        // the prefixes to produce the output names");
        options_description outputs("Output (optional)");
        outputs.add_options()(
            "out", value<std::string>(&out_name),
            "output models (PDBQT), the default is chosen based on the ligand file name")(
            "dir", value<std::string>(&out_dir), "output directory for batch mode")(
            "write_maps", value<std::string>(&out_maps),
            "output filename (directory + prefix name) for maps. Option --force_even_voxels may be "
            "needed to comply with .map format");
        options_description advanced("Advanced options (see the manual)");
        advanced.add_options()("device_id", value<int>(&device_id)->default_value(0),
                               "GPU device id to use (default 0)")(
            "score_only", bool_switch(&score_only), "score only - search space can be omitted")(
            "score_file", value<std::string>(&score_file)->default_value(score_file),
            "score only output file in batch mode, with 'score_only' option")(
            "local_only", bool_switch(&local_only), "do local search only")(
            "no_refine", bool_switch(&no_refine),
            "when --receptor is provided, do not use explicit receptor atoms (instead of "
            "precalculated grids) for: (1) local optimization and scoring after docking, (2) "
            "--local_only jobs, and (3) --score_only jobs")(
            "force_even_voxels", bool_switch(&force_even_voxels),
            "calculated grid maps will have an even number of voxels (intervals) in each dimension "
            "(odd number of grid points)")("randomize_only", bool_switch(&randomize_only),
                                           "randomize input, attempting to avoid clashes")(
            "receptor_weight", value<double>()->default_value(1.0),
            "weight for receptor scoring in hybrid mode")(
            "reference_ligand_weight", value<double>()->default_value(1.0),
            "weight for reference ligand similarity in hybrid mode")(
            "reference_ligand_scale", value<double>()->default_value(2.0),
            "scaling factor for reference ligand LJ parameters")(

            "weight_gauss1", value<double>(&weight_gauss1)->default_value(weight_gauss1),
            "gauss_1 weight")(
                "weight_gauss2", value<double>(&weight_gauss2)->default_value(weight_gauss2),
                "gauss_2 weight")("weight_repulsion",
                                  value<double>(&weight_repulsion)->default_value(weight_repulsion),
                                  "repulsion weight")(
                "weight_hydrophobic",
                value<double>(&weight_hydrophobic)->default_value(weight_hydrophobic),
                "hydrophobic weight")(
                "weight_hydrogen", value<double>(&weight_hydrogen)->default_value(weight_hydrogen),
                "Hydrogen bond weight")(
                "weight_rot", value<double>(&weight_rot)->default_value(weight_rot), "N_rot weight")

                ("weight_vinardo_gauss1",
                 value<double>(&weight_vinardo_gauss1)->default_value(weight_vinardo_gauss1),
                 "Vinardo gauss_1 weight")("weight_vinardo_repulsion",
                                           value<double>(&weight_vinardo_repulsion)
                                               ->default_value(weight_vinardo_repulsion),
                                           "Vinardo repulsion weight")(
                    "weight_vinardo_hydrophobic",
                    value<double>(&weight_vinardo_hydrophobic)
                        ->default_value(weight_vinardo_hydrophobic),
                    "Vinardo hydrophobic weight")(
                    "weight_vinardo_hydrogen",
                    value<double>(&weight_vinardo_hydrogen)->default_value(weight_vinardo_hydrogen),
                    "Vinardo Hydrogen bond weight")(
                    "weight_vinardo_rot", value<double>(&weight_rot)->default_value(weight_rot),
                    "Vinardo N_rot weight")


                        ("weight_glue", value<double>(&weight_glue)->default_value(weight_glue),
                         "macrocycle glue weight")("keep_nonpolar_H",
                                             bool_switch(&keep_H)->default_value(keep_H),
                                             "keep non polar H in sdf")

            ;
        options_description misc("Misc (optional)");
        misc.add_options()(
            "seed", value<int>(&seed)->default_value(0), "explicit random seed")(
            "exhaustiveness", value<int>(&exhaustiveness)->default_value(8),
            "exhaustiveness of the global search (roughly proportional to time): 1+")(
            "max_evals", value<int>(&max_evals)->default_value(0),
            "number of evaluations in each MC run (if zero, which is the default, the number of MC "
            "steps is based on heuristics)")("num_modes", value<int>(&num_modes)->default_value(9),
                                             "maximum number of binding modes to generate")(
            "min_rmsd", value<double>(&min_rmsd)->default_value(1.0),
            "minimum RMSD between output poses")(
            "energy_range", value<double>(&energy_range)->default_value(3.0),
            "maximum energy difference between the best binding mode and the worst one displayed "
            "(kcal/mol)")("spacing", value<double>(&grid_spacing)->default_value(0.375),
                          "grid spacing (Angstrom)")(
            "verbosity", value<int>(&verbosity)->default_value(1),
            "verbosity (0=no output, 1=normal, 2=verbose)")(
            "max_step", value<int>(&max_step)->default_value(0),
            "maximum number of steps in each MC run (if zero, which is the default, the number of "
            "MC steps is based on heuristics)")("refine_step",
                                                value<int>(&refine_step)->default_value(3),
                                                "number of steps in refinement, default=5")(
            "max_gpu_memory", value<int>(&max_gpu_memory)->default_value(0),
            "maximum gpu memory to use (default=0, use all available GPU memory to optain maximum "
            "batch size)")(
            "search_mode", value<std::string>(&search_mode),
            "search mode of vina (fast, balance, detail), using recommended settings of "
            "exhaustiveness and search steps; the higher the computational complexity, the higher "
            "the accuracy, but the larger the computational cost")

            ;
        options_description config("Configuration file (optional)");
        config.add_options()("config", value<std::string>(&config_name),
                             "the above options can be put here");
        options_description info("Information (optional)");
        info.add_options()("help", bool_switch(&help), "display usage summary")(
            "help_advanced", bool_switch(&help_advanced),
            "display usage summary with advanced options")("version", bool_switch(&version),
                                                           "display program version");
        options_description desc, desc_config, desc_simple;
        desc.add(inputs)
            .add(search_area)
            .add(outputs)
            .add(advanced)
            .add(misc)
            .add(config)
            .add(info);
        desc_config.add(inputs).add(search_area).add(outputs).add(advanced).add(misc);
        desc_simple.add(inputs).add(search_area).add(outputs).add(misc).add(config).add(info);

        std::cout << udp_logo << '\n';
        std::cout << version_string << '\n';
        try {
            // store(parse_command_line(argc, argv, desc, command_line_style::default_style ^
            // command_line_style::allow_guessing), vm);
            store(command_line_parser(argc, argv)
                      .options(desc)
                      .style(command_line_style::default_style ^ command_line_style::allow_guessing)
                      .positional(positional)
                      .run(),
                  vm);
            notify(vm);
        } catch (boost::program_options::error& e) {
            std::cerr << "Command line parse error: " << e.what() << '\n'
                      << "\nCorrect usage:\n"
                      << desc_simple << '\n';
            return 1;
        }

        if (vm.count("config")) {
            try {
                path name = make_path(config_name);
                ifile config_stream(name);
                store(parse_config_file(config_stream, desc_config), vm);
                notify(vm);
            } catch (boost::program_options::error& e) {
                std::cerr << "Configuration file parse error: " << e.what() << '\n'
                          << "\nCorrect usage:\n"
                          << desc_simple << '\n';
                return 1;
            }
        }

        if (help) {
            std::cout << desc_simple << '\n';
            return 0;
        }

        if (help_advanced) {
            std::cout << desc << '\n';
            return 0;
        }

        if (version) {
            return 0;
        }

        if (verbosity > 0) {
            std::cout << cite_message << '\n';
        }

        if (vm.count("receptor") && vm.count("maps")) {
            std::cerr << "ERROR: Cannot specify both receptor and affinity maps at the same time, "
                         "--flex argument is allowed with receptor or maps.\n";
            exit(EXIT_FAILURE);
        }

        if (vm.count("search_mode")) {
            if (search_mode.compare("balance") == 0 || search_mode.compare("balanced") == 0) {
                exhaustiveness = 384;
                max_step = 40;
            }
            if (search_mode.compare("fast") == 0) {
                exhaustiveness = 128;
                max_step = 20;
            }
            if (search_mode.compare("detail") == 0 || search_mode.compare("detailed") == 0) {
                exhaustiveness = 512;
                max_step = 40;
            }
        } else if ((vm.count("gpu_batch") || vm.count("ligand_index"))
                   && !vm.count("exhaustiveness")) {
            exhaustiveness = 384;
            max_step = 40;
        }


        // 在程序逻辑部分添加模式判断
        std::string reference_ligand_name;
        double receptor_weight = 1.0;
        double reference_ligand_weight = 1.0;
        double reference_ligand_scale = 1.0;
        bool pure_docking = false;
        bool similarity_searching = false;
        bool hybrid_mode = false;
        
        // 获取reference_ligand参数
        if (vm.count("reference_ligand")) {
            reference_ligand_name = vm["reference_ligand"].as<std::string>();
        }
        
        // 获取权重参数
        if (vm.count("receptor_weight")) {
            receptor_weight = vm["receptor_weight"].as<double>();
        }
        
        if (vm.count("reference_ligand_weight")) {
            reference_ligand_weight = vm["reference_ligand_weight"].as<double>();
        }
        if (vm.count("reference_ligand_scale")) {
            reference_ligand_scale = vm["reference_ligand_scale"].as<double>();
        }
        
        // 根据参数存在情况判断模式
        if (vm.count("receptor") && !vm.count("reference_ligand")) {
            pure_docking = true;
        } else if (!vm.count("receptor") && !vm.count("maps") && vm.count("reference_ligand")) {
            similarity_searching = true;
            // no_refine = true;
        } else if (vm.count("receptor") && vm.count("reference_ligand")) {
            hybrid_mode = true;
        }

        // 修改原始验证逻辑，考虑reference_ligand的情况
        if (sf_name.compare("vina") == 0 || sf_name.compare("vinardo") == 0) {
            if (!vm.count("receptor") && !vm.count("maps") && !vm.count("reference_ligand")) {
                std::cerr << desc_simple
                          << "ERROR: Either the receptor, affinity maps, or a reference ligand must be specified.\n";
                exit(EXIT_FAILURE);
            }
        } else {
            std::cerr << desc_simple << "Scoring function " << sf_name << " unknown.\n";
            exit(EXIT_FAILURE);
        }

        // 输出选择的模式
        if (verbosity > 0) {
            if (pure_docking) {
                std::cout << "Mode: Pure docking\n";
            } else if (similarity_searching) {
                std::cout << "Mode: Similarity searching\n";
                std::cout << "Reference ligand: " << reference_ligand_name << "\n";
                std::cout << "Reference ligand weight: " << reference_ligand_weight << "\n";
                std::cout << "Reference ligand scale: " << reference_ligand_scale << "\n";
            } else if (hybrid_mode) {
                std::cout << "Mode: Hybrid (docking + similarity)\n";
                std::cout << "Reference ligand: " << reference_ligand_name << "\n";
                std::cout << "Receptor weight: " << receptor_weight << "\n";
                std::cout << "Reference ligand weight: " << reference_ligand_weight << "\n";
                std::cout << "Reference ligand scale: " << reference_ligand_scale << "\n";
            }
        }

        if (!vm.count("ligand") && !vm.count("batch") && !vm.count("gpu_batch")
            && !vm.count("ligand_index") && !vm.count("gpu_batch_sdf")) {
            std::cerr << desc_simple << "\n\nERROR: Missing ligand(s).\n";
            exit(EXIT_FAILURE);
        } else if (vm.count("ligand") && (vm.count("batch") || vm.count("gpu_batch"))) {
            std::cerr
                << desc_simple
                << "\n\nERROR: Can't use both --ligand and --batch arguments simultaneously.\n";
            exit(EXIT_FAILURE);
        } else if ((vm.count("batch") || vm.count("gpu_batch")) && !vm.count("dir")) {
            std::cerr << desc_simple
                      << "\n\nERROR: Need to specify an output directory for batch mode.\n";
            exit(EXIT_FAILURE);
        } else if (vm.count("dir")) {
            if (!is_directory(out_dir)) {
                std::cerr << "ERROR: Directory " << out_dir << " does not exist.\n";
                exit(EXIT_FAILURE);
            }
        } else if (vm.count("ligand") && vm.count("dir")) {
            std::cerr << "WARNING: In ligand mode, --dir argument is ignored.\n";
        }

        if (!score_only) {
            if (!vm.count("out") && ligand_names.size() == 1) {
                out_name = default_output(ligand_names[0]);
                std::cout << "Output will be " << out_name << '\n';
            } else if (!vm.count("out") && ligand_names.size() >= 1) {
                std::cerr << desc_simple
                          << "\n\nERROR: Output name must be defined when docking simultaneously "
                             "multiple ligands.\n";
                exit(EXIT_FAILURE);
            }
        }

        // read ligands from index file
        // will append to `batch` if used together
        if (vm.count("ligand_index")) {
            std::ifstream index_file(ligand_index);
            if (!index_file.is_open()) {
                throw file_error(ligand_index, true);
            }
            std::string ligand_name;
            while (index_file >> ligand_name) {
                gpu_batch_ligand_names.push_back(ligand_name);
            }
            index_file.close();
        }

        if (verbosity > 0) {
            std::cout << "Scoring function : " << sf_name << "\n";
            if (vm.count("receptor")) std::cout << "Rigid receptor: " << rigid_name << "\n";
            if (vm.count("flex")) std::cout << "Flex receptor: " << flex_name << "\n";
            if (ligand_names.size() == 1) {
                std::cout << "Ligand: " << ligand_names[0] << "\n";
            } else if (ligand_names.size() > 1) {
                std::cout << "Ligands:\n";
                VINA_RANGE(i, 0, ligand_names.size()) {
                    std::cout << "  - " << ligand_names[i] << "\n";
                }
            } else if (batch_ligand_names.size() > 1) {
                std::cout << "Ligands (batch mode): " << batch_ligand_names.size()
                          << " molecules\n";
            }
            if (!vm.count("maps") && !autobox) {
                std::cout << "Grid center: X " << center_x << " Y " << center_y << " Z " << center_z
                          << "\n";
                std::cout << "Grid size  : X " << size_x << " Y " << size_y << " Z " << size_z
                          << "\n";
                std::cout << "Grid space : " << grid_spacing << "\n";
            } else if (autobox) {
                std::cout << "Grid center: ligand center (autobox)\n";
                std::cout << "Grid size  : ligand size + " << buffer_size
                          << " A in each dimension (autobox)\n";
                std::cout << "Grid space : " << grid_spacing << "\n";
            }
            std::cout << "Exhaustiveness: " << exhaustiveness << "\n";
            if (!vm.count("seed")) std::cout << "Seed: " << seed << "\n";
            std::cout << "Verbosity: " << verbosity << "\n";
            std::cout << "\n";
        }

        Vina v(sf_name, seed, verbosity, no_refine);

        v.receptor_weight = receptor_weight;
        v.reference_ligand_weight = reference_ligand_weight;
        v.reference_ligand_scale = reference_ligand_scale;
        v.pure_docking = pure_docking;
        v.similarity_searching = similarity_searching;
        v.hybrid_mode = hybrid_mode;

        if (similarity_searching || hybrid_mode) {
            v.set_reference_ligand(reference_ligand_name);
        }

        if (vm.count("receptor") || vm.count("flex")) v.set_receptor(rigid_name, flex_name);


        // Technically we don't have to initialize weights,
        // because they are initialized during the Vina object creation with the default weights
        // but we still do it in case the user decided to change them
        if (sf_name.compare("vina") == 0) {
            v.set_vina_weights(weight_gauss1, weight_gauss2, weight_repulsion, weight_hydrophobic,
                               weight_hydrogen, weight_glue, weight_rot);
        } else if (sf_name.compare("vinardo") == 0) {
            v.set_vinardo_weights(weight_vinardo_gauss1, weight_vinardo_repulsion,
                                  weight_vinardo_hydrophobic, weight_vinardo_hydrogen, weight_glue,
                                  weight_rot);
        }

        if (vm.count("ligand")) {
            std::vector<model> ligands;
            VINA_FOR_IN(i, ligand_names) {
                ligands.emplace_back(parse_ligand_from_file_no_failure(
                    ligand_names[i], v.m_scoring_function->get_atom_typing(), keep_H));
            }
            v.set_ligand_from_object(ligands);

            if (sf_name.compare("vina") == 0 || sf_name.compare("vinardo") == 0) {
                if (vm.count("maps")) {
                    v.load_maps(maps);
                } else {
                    // Will compute maps only for Vina atom types in the ligand(s)
                    // In the case users ask for score and local only with the autobox arg, we
                    // compute the optimal box size for it/them.
                    if ((score_only || local_only) && autobox) {
                        std::vector<double> dim = v.grid_dimensions_from_ligand(buffer_size);
                        v.compute_vina_maps(dim[0], dim[1], dim[2], dim[3], dim[4], dim[5],
                                            grid_spacing, force_even_voxels);
                    } else {
                        v.compute_vina_maps(center_x, center_y, center_z, size_x, size_y, size_z,
                                            grid_spacing, force_even_voxels);
                    }

                    if (vm.count("write_maps")) v.write_maps(out_maps);
                }
            }

            if (randomize_only) {
                v.randomize();
                v.write_pose(out_name);
            } else if (score_only) {
                std::vector<double> energies;
                energies = v.score();
                v.show_score(energies);
                v.write_score(energies, ligand_names[0]);
            } else if (local_only) {
                std::vector<double> energies;
                energies = v.optimize();
                v.write_pose(out_name);
                v.show_score(energies);
            } else {
                // search one ligand on GPU
                v.enable_gpu();
                v.global_search_gpu(exhaustiveness, num_modes, min_rmsd, max_evals,
                                    max_step, 1, (unsigned long long)seed,
                                    refine_step, local_only, true);
                v.write_poses_gpu({out_name}, num_modes, energy_range);
            }
        } else if (vm.count("gpu_batch") || vm.count("ligand_index")) {
            if (randomize_only) {
                printf("Not available under gpu_batch mode.\n");
                return 0;
            }
            v.enable_gpu();
            if (sf_name.compare("vina") == 0 || sf_name.compare("vinardo") == 0) {
                if (vm.count("maps")) {
                    v.load_maps(maps);
                } else {
                    // Will compute maps for all Vina atom types
                    v.compute_vina_maps(center_x, center_y, center_z, size_x, size_y, size_z,
                                        grid_spacing, force_even_voxels);

                    if (vm.count("write_maps")) v.write_maps(out_maps);
                }
            }

            // bool index = 0; // indicate which worker occupies GPU
            std::vector<std::string> ligand_names{std::move(gpu_batch_ligand_names)};
            std::cout << "Total ligands: " << ligand_names.size() << std::endl;

            if (score_only) {
                VINA_FOR_IN(i, ligand_names) {
                    std::vector<model> ligands;
                    ligands.emplace_back(parse_ligand_from_file_no_failure(
                        ligand_names[i], v.m_scoring_function->get_atom_typing(), keep_H));
                    Vina v1(v);
                    v1.set_ligand_from_object(ligands);
                    std::vector<double> energies;
                    energies = v1.score();
                    v1.show_score(energies);
                    v1.write_score_to_file(energies, out_dir, score_file, ligand_names[i]);
                }
                return 0;
            }

            int receptor_atom_numbers = v.m_receptor.get_atoms().size();
            int deviceCount = 0;
            size_t avail;
            size_t total;
            // get total memory in MB and leave 5%
            float max_memory
                = sysconf(_SC_PHYS_PAGES) * sysconf(_SC_PAGE_SIZE) / 1024 / 1024 * 0.95;
            bool use_v100 = true;
            cudaGetDeviceCount(&deviceCount);
            if (deviceCount > 0) {
                checkCUDA(cudaSetDevice(device_id));

                printf("Set GPU device id to %d\n", device_id);
                cudaMemGetInfo(&avail, &total);
                printf("Available Memory = %dMiB   Total Memory = %dMiB\n",
                       int(avail / 1024 / 1024), int(total / 1024 / 1024));
                max_gpu_memory = avail / 1024 / 1024 * 0.95;  // leave 5%
            }

            if (max_gpu_memory > 0 && max_gpu_memory < max_memory) {
                max_memory = (float)max_gpu_memory;
            }

            typedef std::pair<std::string, model> named_model;
            std::vector<named_model> all_ligands;
            const int ligand_batch_limit = 1e6;  // ~20GB for 100,000 lig obj
            int batch_index = 0;
            while (batch_index < ligand_names.size()) {
                all_ligands.clear();
                int next_batch_index
                    = std::min(int(batch_index + ligand_batch_limit), int(ligand_names.size()));
#pragma omp parallel for
                for (int ligand_count = batch_index; ligand_count < next_batch_index;
                     ++ligand_count) {
                    auto& ligand = ligand_names[ligand_count];
                    auto l = parse_ligand_from_file_no_failure(
                        ligand, v.m_scoring_function->get_atom_typing(), keep_H);
#pragma omp critical
                    all_ligands.emplace_back(std::make_pair(ligand, l));
                }
                batch_index = next_batch_index;
                /*
                std::sort(all_ligands.begin(), all_ligands.end(),
                          [](named_model a, named_model b)
                          { return a.second.get_atoms().size() < b.second.get_atoms().size(); });
                */
                DEBUG_PRINTF("%d\n", next_batch_index);
                int processed_ligands = 0;
                int batch_id = 0;
                std::vector<size_t>num_atoms_vector(all_ligands.size());
                std::vector<size_t>num_torsions_vector(all_ligands.size());
                std::vector<size_t>num_rigids_vector(all_ligands.size());
                std::vector<size_t>num_lig_pairs_vector(all_ligands.size());
                size_t max_num_atoms = 0;
                size_t max_num_ligands = 0;
                size_t max_num_torsions = 0;
                size_t max_num_rigids = 0;
                size_t max_num_lig_pairs = 0;
                printf("all_ligands.size():%ld\n",all_ligands.size());
                for (int i = 0; i <  all_ligands.size(); ++i) {
                    // printf("i=:%d\n",i);
                    num_atoms_vector.at(i) = all_ligands[i].second.num_atoms();
                    num_torsions_vector.at(i)=sum(all_ligands[i].second.ligands.count_torsions());
                    num_rigids_vector.at(i)=all_ligands[i].second.ligands[0].children.size();
                    num_lig_pairs_vector.at(i)=all_ligands[i].second.num_internal_pairs();
                    max_num_atoms = std::max(max_num_atoms, num_atoms_vector.at(i));
                    max_num_torsions = std::max(max_num_torsions, num_torsions_vector.at(i));
                    max_num_rigids = std::max(max_num_rigids,num_rigids_vector.at(i));
                    max_num_lig_pairs = std::max(max_num_lig_pairs,num_lig_pairs_vector.at(i));
                }
                
                printf("max_num_atoms:%ld\n",max_num_atoms);
                printf("max_num_torsions:%ld\n",max_num_torsions);
                printf("max_num_rigids:%ld\n",max_num_rigids);
                printf("max_num_lig_pairs:%ld\n",max_num_lig_pairs);
                std::vector<Ligand> ligands;
                for (int i = 0; i < all_ligands.size(); i++) {
                    Ligand lig;
                    lig.num_atoms = num_atoms_vector.at(i);
                    lig.num_torsions = num_torsions_vector.at(i);
                    lig.num_rigids = num_rigids_vector.at(i);
                    lig.num_lig_pairs = num_lig_pairs_vector.at(i);
                    lig.index = i;
                    lig.score = calculateScore(lig);
                    ligands.push_back(lig);
                }

                std::vector<Ligand> smallGroup;
                std::vector<Ligand> mediumGroup;
                std::vector<Ligand> largeGroup;
                std::vector<Ligand> extraLargeGroup;
                std::vector<Ligand> maxGroup;
                classifyLigands(ligands,smallGroup,mediumGroup,largeGroup,extraLargeGroup,maxGroup);
                std::cout << "Small Group:" << std::endl;
                printMaxValues(smallGroup);
                
                std::cout << "Medium Group:" << std::endl;
                if (!mediumGroup.empty()) printMaxValues(mediumGroup);
                
                std::cout << "Large Group:" << std::endl;
                if (!largeGroup.empty()) printMaxValues(largeGroup);
                
                std::cout << "Extra Large Group:" << std::endl;
                if (!extraLargeGroup.empty()) printMaxValues(extraLargeGroup);
                int processed_ligands_smile = 0;
                int smile_batch_id = 0;
                int processed_ligands_medium = 0;
                int medium_batch_id = 0;
                int processed_ligands_large = 0;
                int large_batch_id = 0;
                int processed_ligands_extra_large = 0;
                int extra_larg_batch_id = 0;
                template_batch_docking<SmallConfig>(v,all_ligands,smallGroup,"Small",exhaustiveness, max_memory,
                        receptor_atom_numbers, out_dir, num_modes,
                        min_rmsd,max_evals,max_step,seed, refine_step, local_only, energy_range);
                template_batch_docking<MediumConfig>(v,all_ligands,mediumGroup,"Medium",exhaustiveness, max_memory,
                        receptor_atom_numbers, out_dir, num_modes,
                        min_rmsd,max_evals,max_step,seed, refine_step, local_only, energy_range);
                template_batch_docking<LargeConfig>(v,all_ligands,largeGroup,"Large",exhaustiveness, max_memory,
                        receptor_atom_numbers, out_dir, num_modes,
                        min_rmsd,max_evals,max_step,seed, refine_step, local_only, energy_range);
                template_batch_docking<ExtraLargeConfig>(v,all_ligands,extraLargeGroup,"Extra Large",exhaustiveness, max_memory,
                        receptor_atom_numbers, out_dir, num_modes,
                        min_rmsd,max_evals,max_step,seed, refine_step, local_only, energy_range);
                template_batch_docking<MaxConfig>(v,all_ligands,maxGroup,"Max",exhaustiveness, max_memory,
                        receptor_atom_numbers, out_dir, num_modes,
                        min_rmsd,max_evals,max_step,seed, refine_step, local_only, energy_range);
            }
        }
    }

    catch (file_error& e) {
        std::cerr << "\n\nError: could not open \"" << e.name.string() << "\" for "
                  << (e.in ? "reading" : "writing") << ".\n";
        return 1;
    } catch (boost::filesystem::filesystem_error& e) {
        std::cerr << "\n\nFile system error: " << e.what() << '\n';
        return 1;
    } catch (usage_error& e) {
        std::cerr << "\n\nUsage error: " << e.what() << ".\n";
        return 1;
    }
#ifdef NDEBUG  // don't catch in debug mode
    catch (std::bad_alloc&) {
        std::cerr << "\n\nError: insufficient memory!\n";
        return 1;
    }

    // Errors that shouldn't happen:
    catch (std::exception& e) {
        std::cerr << "\n\nAn error occurred: " << e.what() << ". " << error_message;
        return 1;
    } catch (internal_error& e) {
        std::cerr << "\n\nAn internal error occurred in " << e.file << "(" << e.line << "). "
                  << error_message;
        return 1;
    } catch (...) {
        std::cerr << "\n\nAn unknown error occurred. " << error_message;
        return 1;
    }
#endif  // NDEBUG
}
