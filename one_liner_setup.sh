#!/bin/bash
# One-liner setup for UniDock-Pro - copy and paste this entire command after SSH connection

apt update && apt install -y build-essential cmake git libboost-system-dev libboost-thread-dev libboost-serialization-dev libboost-filesystem-dev libboost-program-options-dev libboost-timer-dev && git clone https://github.com/NiBoyang/UniDock-Pro.git && cd UniDock-Pro && cmake -B build -DCMAKE_BUILD_TYPE=Release && cmake --build build -j$(nproc) && echo "Build complete! Testing..." && cd example && ../build/udp --receptor ./receptor/rec.pdbqt --reference_ligand ./ref_lig/xtal_lig.pdbqt --ligand_index ligand_index.txt --center_x 32.790 --center_y 38.342 --center_z 58.486 --size_x 28 --size_y 28 --size_z 28 --search_mode balance --dir ./test_results && echo "✅ UniDock-Pro setup complete! Binary at: $(pwd)/../build/udp"
