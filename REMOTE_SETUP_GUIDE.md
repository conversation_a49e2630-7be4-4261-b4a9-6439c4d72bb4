# UniDock-Pro Remote Server Setup Guide

## Connection Information
```bash
ssh -p 31030 <EMAIL>
# Password: MxDVoFLUo5qq.
```

## Option 1: Automated Setup (Recommended)

### Full Setup (includes CUDA installation if needed)
```bash
# Upload and run the complete setup script
wget https://raw.githubusercontent.com/your-repo/remote_setup.sh
chmod +x remote_setup.sh
./remote_setup.sh
```

### Quick Setup (if CUDA already installed)
```bash
# Upload and run the quick setup script
wget https://raw.githubusercontent.com/your-repo/quick_setup.sh
chmod +x quick_setup.sh
./quick_setup.sh
```

## Option 2: Manual Setup

### 1. System Preparation
```bash
# Update system
apt update && apt upgrade -y

# Install build tools
apt install -y build-essential cmake git wget curl
```

### 2. Install CUDA (if not present)
```bash
# Check if CUDA is installed
nvcc --version
nvidia-smi

# If not installed, install CUDA 11.8+
wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2004/x86_64/cuda-keyring_1.0-1_all.deb
dpkg -i cuda-keyring_1.0-1_all.deb
apt update
apt install -y cuda-toolkit-11-8

# Add to PATH
echo 'export PATH=/usr/local/cuda/bin:$PATH' >> ~/.bashrc
echo 'export LD_LIBRARY_PATH=/usr/local/cuda/lib64:$LD_LIBRARY_PATH' >> ~/.bashrc
source ~/.bashrc
```

### 3. Install Boost Libraries
```bash
apt install -y \
  libboost-system-dev libboost-thread-dev libboost-serialization-dev \
  libboost-filesystem-dev libboost-program-options-dev libboost-timer-dev
```

### 4. Clone and Build UniDock-Pro
```bash
# Clone repository
git clone https://github.com/NiBoyang/UniDock-Pro.git
cd UniDock-Pro

# Build
cmake -B build -DCMAKE_BUILD_TYPE=Release
cmake --build build -j$(nproc)
```

### 5. Test Installation
```bash
# Test the binary
./build/udp --help

# Run example
cd example
../build/udp \
  --receptor ./receptor/rec.pdbqt \
  --reference_ligand ./ref_lig/xtal_lig.pdbqt \
  --ligand_index ligand_index.txt \
  --center_x 32.790 --center_y 38.342 --center_z 58.486 \
  --size_x 28 --size_y 28 --size_z 28 \
  --search_mode balance \
  --dir ./results
```

## Usage Examples

### Classical Docking
```bash
./build/udp \
  --receptor receptor.pdbqt \
  --ligand_index ligands.txt \
  --center_x 0 --center_y 0 --center_z 0 \
  --size_x 20 --size_y 20 --size_z 20 \
  --search_mode balance \
  --dir ./results
```

### Ligand Similarity Search
```bash
./build/udp \
  --reference_ligand reference.pdbqt \
  --ligand_index ligands.txt \
  --center_x 0 --center_y 0 --center_z 0 \
  --size_x 20 --size_y 20 --size_z 20 \
  --search_mode fast \
  --dir ./results
```

### Hybrid Docking
```bash
./build/udp \
  --receptor receptor.pdbqt \
  --reference_ligand reference.pdbqt \
  --ligand_index ligands.txt \
  --center_x 0 --center_y 0 --center_z 0 \
  --size_x 20 --size_y 20 --size_z 20 \
  --search_mode detail \
  --dir ./results
```

## File Transfer Commands

### Upload files to remote server
```bash
# Upload single file
scp -P 31030 local_file.pdbqt <EMAIL>:/root/

# Upload directory
scp -P 31030 -r local_directory/ <EMAIL>:/root/

# Upload with rsync (better for large files)
rsync -avz -e "ssh -p 31030" local_directory/ <EMAIL>:/root/
```

### Download results from remote server
```bash
# Download results directory
scp -P 31030 -r <EMAIL>:/root/UniDock-Pro/results/ ./

# Download specific files
scp -P 31030 <EMAIL>:/root/UniDock-Pro/results/*.pdbqt ./
```

## Troubleshooting

### Check GPU
```bash
nvidia-smi
```

### Check CUDA
```bash
nvcc --version
```

### Check Build Dependencies
```bash
cmake --version
g++ --version
dpkg -l | grep libboost
```

### Common Issues
1. **CUDA not found**: Ensure CUDA toolkit is installed and in PATH
2. **Boost version too old**: Install from source or use conda
3. **Build fails**: Check all dependencies are installed
4. **GPU memory issues**: Use smaller batch sizes or reduce search space

## Performance Tips
- Use `--search_mode fast` for quick screening
- Use `--search_mode balance` for good speed/accuracy trade-off
- Use `--search_mode detail` for highest accuracy
- Monitor GPU memory usage with `nvidia-smi`
- Use multiple GPUs if available
