#!/bin/bash

# Quick UniDock-Pro Setup Script
# Minimal setup assuming CUDA is already installed

set -e

echo "=== Quick UniDock-Pro Setup ==="

# Install dependencies
echo "Installing dependencies..."
apt update
apt install -y build-essential cmake git \
    libboost-system-dev libboost-thread-dev libboost-serialization-dev \
    libboost-filesystem-dev libboost-program-options-dev libboost-timer-dev

# Clone and build
echo "Cloning UniDock-Pro..."
git clone https://github.com/NiBoyang/UniDock-Pro.git
cd UniDock-Pro

echo "Building..."
cmake -B build -DCMAKE_BUILD_TYPE=Release
cmake --build build -j$(nproc)

echo "Testing..."
cd example
../build/udp \
  --receptor ./receptor/rec.pdbqt \
  --reference_ligand ./ref_lig/xtal_lig.pdbqt \
  --ligand_index ligand_index.txt \
  --center_x 32.790 --center_y 38.342 --center_z 58.486 \
  --size_x 28 --size_y 28 --size_z 28 \
  --search_mode balance \
  --dir ./results

echo "✓ Setup complete! Binary at: $(pwd)/../build/udp"
