#!/bin/bash

# UniDock-Pro Remote Server Setup Script
# This script sets up UniDock-Pro on a remote server with CUDA support

set -e  # Exit on any error

echo "=== UniDock-Pro Remote Server Setup ==="
echo "Starting setup process..."

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check CUDA installation
check_cuda() {
    if command_exists nvcc; then
        echo "✓ CUDA Toolkit found: $(nvcc --version | grep release)"
        return 0
    else
        echo "✗ CUDA Toolkit not found"
        return 1
    fi
}

# Function to check GPU availability
check_gpu() {
    if command_exists nvidia-smi; then
        echo "✓ GPU detected:"
        nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits
        return 0
    else
        echo "✗ No GPU detected or nvidia-smi not available"
        return 1
    fi
}

# Update system packages
echo "Updating system packages..."
apt update && apt upgrade -y

# Install essential build tools
echo "Installing essential build tools..."
apt install -y \
    build-essential \
    cmake \
    git \
    wget \
    curl \
    software-properties-common

# Check and install CUDA if needed
echo "Checking CUDA installation..."
if ! check_cuda; then
    echo "Installing CUDA Toolkit..."
    # Add NVIDIA package repository
    wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2004/x86_64/cuda-keyring_1.0-1_all.deb
    dpkg -i cuda-keyring_1.0-1_all.deb
    apt update
    
    # Install CUDA toolkit (version 11.8 or higher)
    apt install -y cuda-toolkit-11-8
    
    # Add CUDA to PATH
    echo 'export PATH=/usr/local/cuda/bin:$PATH' >> ~/.bashrc
    echo 'export LD_LIBRARY_PATH=/usr/local/cuda/lib64:$LD_LIBRARY_PATH' >> ~/.bashrc
    source ~/.bashrc
    
    echo "CUDA installation completed. Please reboot the system if this is the first CUDA installation."
fi

# Install Boost libraries
echo "Installing Boost libraries..."
apt install -y \
    libboost-system-dev \
    libboost-thread-dev \
    libboost-serialization-dev \
    libboost-filesystem-dev \
    libboost-program-options-dev \
    libboost-timer-dev

# Check Boost version
echo "Checking Boost version..."
dpkg -l | grep libboost-dev || echo "Boost version check failed, but libraries should be installed"

# Clone UniDock-Pro repository
echo "Cloning UniDock-Pro repository..."
if [ -d "UniDock-Pro" ]; then
    echo "UniDock-Pro directory already exists. Updating..."
    cd UniDock-Pro
    git pull
    cd ..
else
    git clone https://github.com/NiBoyang/UniDock-Pro.git
fi

# Build UniDock-Pro
echo "Building UniDock-Pro..."
cd UniDock-Pro

# Create build directory
mkdir -p build
cd build

# Configure with CMake
echo "Configuring build with CMake..."
cmake .. -DCMAKE_BUILD_TYPE=Release

# Build the project
echo "Compiling UniDock-Pro (this may take several minutes)..."
cmake --build . -j$(nproc)

# Verify build
if [ -f "udp" ]; then
    echo "✓ Build successful! UniDock-Pro binary 'udp' created."
    
    # Make binary executable
    chmod +x udp
    
    # Show help to verify it works
    echo "Testing UniDock-Pro binary..."
    ./udp --help | head -10
    
else
    echo "✗ Build failed! Binary 'udp' not found."
    exit 1
fi

# Go back to UniDock-Pro root
cd ..

# Test with example data
echo "Testing with example data..."
cd example

# Run a quick test
echo "Running example hybrid docking..."
../build/udp \
  --receptor ./receptor/rec.pdbqt \
  --reference_ligand ./ref_lig/xtal_lig.pdbqt \
  --ligand_index ligand_index.txt \
  --center_x 32.790 --center_y 38.342 --center_z 58.486 \
  --size_x 28 --size_y 28 --size_z 28 \
  --search_mode balance \
  --dir ./test_results

# Check if test results were created
if [ -d "test_results" ]; then
    echo "✓ Test run successful! Results created in example/test_results/"
    ls -la test_results/
else
    echo "✗ Test run failed! No results directory created."
fi

# Go back to root
cd ..

echo ""
echo "=== Setup Summary ==="
check_gpu
check_cuda
echo "✓ UniDock-Pro built successfully"
echo "✓ Binary location: $(pwd)/build/udp"
echo "✓ Example test completed"
echo ""
echo "=== Usage Instructions ==="
echo "To run UniDock-Pro:"
echo "  cd $(pwd)"
echo "  ./build/udp --help"
echo ""
echo "Example command:"
echo "  ./build/udp \\"
echo "    --receptor receptor.pdbqt \\"
echo "    --ligand_index ligands.txt \\"
echo "    --center_x 0 --center_y 0 --center_z 0 \\"
echo "    --size_x 20 --size_y 20 --size_z 20 \\"
echo "    --search_mode balance \\"
echo "    --dir ./results"
echo ""
echo "Setup completed successfully!"
